# Prediction Module

This module handles prediction functionality for decision tree models, including data validation and inference.

## Features

- **Input Data Validation**: Comprehensive validation of prediction data against model constraints
- **Type Safety**: Ensures data types match model expectations
- **Range Validation**: Validates numeric values are within expected ranges
- **Categorical Validation**: Ensures categorical values are from valid sets
- **Missing Value Handling**: Graceful handling of missing/NA values
- **Batch Processing**: Efficient validation of large datasets
- **Detailed Error Reporting**: Clear error messages for debugging

## Structure

```
internals/prediction/
├── readme.md              # This file
├── validation.go          # Input validation functionality
├── validation_test.go     # Comprehensive validation tests
├── predictor.go          # Main prediction logic (planned)
└── predictor_test.go     # Tests for prediction functionality (planned)
```

## Validation Functionality

### Core Functions

- **`ValidateRecords()`**: Validates prediction records against model constraints
- **`ValidateAndFilter()`**: Convenience function that returns only valid records
- **`ValidationResult`**: Comprehensive result structure with valid/invalid records and errors

### Validation Types

1. **Feature Presence**: Ensures all required features are present
2. **Type Validation**: Verifies data types match feature expectations
3. **Range Validation**: Checks numeric values are within defined ranges
4. **Categorical Validation**: Ensures categorical values are from valid sets
5. **Missing Value Handling**: Allows nil values for missing data

## Usage Examples

### Basic Validation
```go
import (
    "github.com/berrijam/mulberri/internals/prediction"
    "github.com/berrijam/mulberri/internals/utils"
)

// Load prediction data
headers, records, err := utils.LoadPredictionDataFromModel("data.csv", model)
if err != nil {
    log.Fatal(err)
}

// Validate records
result, err := prediction.ValidateRecords(records, model)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Valid records: %d\n", len(result.ValidRecords))
fmt.Printf("Invalid records: %d\n", len(result.InvalidRecords))
fmt.Printf("Validation errors: %d\n", len(result.Errors))
```

### Filter Valid Records Only
```go
// Get only valid records (convenience function)
validRecords, err := prediction.ValidateAndFilter(records, model)
if err != nil {
    log.Fatal(err)
}

// Use validRecords for prediction
```

### Handle Validation Errors
```go
result, err := prediction.ValidateRecords(records, model)
if err != nil {
    log.Fatal(err)
}

// Process validation errors
for _, validationErr := range result.Errors {
    fmt.Printf("Row %d, Field %s: %s\n",
        validationErr.RowIdx, validationErr.Field, validationErr.Reason)
}
```

## Implementation Status

✅ **Validation Module**: Complete with comprehensive testing
🚧 **Prediction Engine**: Under development

### Completed Components

- **✅ Data Validation**: Full validation against model constraints
- **✅ Error Handling**: Structured error types with detailed messages
- **✅ Type Safety**: Runtime type checking and conversion
- **✅ Range Validation**: Numeric range constraints
- **✅ Categorical Validation**: Valid value set checking
- **✅ Missing Value Support**: Graceful handling of nil values
- **✅ Comprehensive Testing**: 9 test functions covering all scenarios

### Planned Components

- **🚧 Model Loader**: Load saved decision tree models
- **🚧 Predictor**: Generate predictions from validated input data
- **🚧 Confidence Scoring**: Prediction confidence calculation
- **🚧 Exporter**: Format and save prediction results

## Dependencies

- `pkg/models`: Decision tree model definitions and feature constraints
- `internals/utils`: Data loading utilities and PredictionRecord types