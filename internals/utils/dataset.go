// Package utils provides dataset utilities for machine learning operations.
package utils

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// CSVDataset implements training.Dataset[string] for CSV data
type CSVDataset struct {
	features [][]string
	targets  []string
	indices  []int
}

// NewCSVDataset creates a new CSV dataset
func NewCSVDataset(features [][]string, targets []string) training.Dataset[string] {
	if len(features) != len(targets) {
		errMsg := fmt.Sprintf("Features and targets length mismatch: %d vs %d", len(features), len(targets))
		logger.Fatal(errMsg)
	}

	indices := make([]int, len(targets))
	for i := range indices {
		indices[i] = i
	}

	return &CSVDataset{
		features: features,
		targets:  targets,
		indices:  indices,
	}
}

// GetSize returns the number of samples in the current dataset
func (d *CSVDataset) GetSize() int {
	return len(d.indices)
}

// GetFeatureValue retrieves a feature value for a given sample and feature
func (d *CSVDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.features) {
		return nil, fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.features))
	}

	if feature.ColumnNumber >= len(d.features[sampleIdx]) {
		return nil, fmt.Errorf("feature column %d out of range for sample %d", feature.ColumnNumber, sampleIdx)
	}

	value := d.features[sampleIdx][feature.ColumnNumber]

	// Convert based on feature type
	switch feature.Type {
	case models.NumericFeature:
		if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			return floatVal, nil
		}
		return value, nil
	case models.CategoricalFeature:
		return value, nil
	default:
		return value, nil
	}
}

// GetTarget retrieves the target value for a given sample
func (d *CSVDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.targets) {
		return "", fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.targets))
	}

	return d.targets[sampleIdx], nil
}

// GetIndices returns the current sample indices
func (d *CSVDataset) GetIndices() []int {
	return d.indices
}

// Subset creates a new dataset view with the specified indices
// This is a zero-copy operation that creates a new view without duplicating data
func (d *CSVDataset) Subset(indices []int) training.Dataset[string] {
	return &CSVDataset{
		features: d.features,
		targets:  d.targets,
		indices:  indices,
	}
}
