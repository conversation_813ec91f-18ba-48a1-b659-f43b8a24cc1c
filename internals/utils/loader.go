// Package utils provides utilities for loading and validating machine learning datasets.
//
// This package offers robust CSV loading with comprehensive validation, metadata parsing
// for feature type definitions, and utilities for data preprocessing. It emphasizes
// data integrity, clear error reporting, and performance for large datasets.
//
// Key features:
//   - RFC 4180 compliant CSV parsing with proper quote handling
//   - Comprehensive input validation with structured error types
//   - YAML-based feature metadata parsing with Unicode support
//   - Memory-efficient data processing with pre-allocation
//   - Configurable NA value detection and handling
//
// Basic usage for CSV loading:
//
//	headers, data, targetIdx, err := utils.ReadTrainingCSV("data.csv", "target_column")
//	if err != nil {
//	    log.Fatal(err)
//	}
//
//	features, targets, featureHeaders, err := utils.ExtractFeatureAndTarget(
//	    data, headers, "target_column", targetIdx)
//	if err != nil {
//	    log.Fatal(err)
//	}
//
// For metadata parsing:
//
//	parser := utils.NewFeatureInfoParser()
//	config, err := parser.ParseFeatureInfoFile("features.yaml")
//	if err != nil {
//	    log.Fatal(err)
//	}
//
// Error handling:
//
//	All functions return structured error types that can be type-asserted for
//	specific error handling:
//
//	if csvErr, ok := err.(*utils.CSVError); ok {
//	    fmt.Printf("CSV error at line %d: %v\n", csvErr.Line, csvErr.Err)
//	}
package utils

import (
	"bufio"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
)

// Constants for configuration
const (
	MinRequiredDataRows = 2
	DefaultDelimiter    = ','
)

// Configurable NA values
var DefaultNAValues = []string{
	"na", "nan", "n/a", "null", "none",
	"missing", "unknown", "undefined",
}

// CSVError represents errors that occur during CSV processing
type CSVError struct {
	Op     string
	File   string
	Line   int
	Column string
	Err    error
}

func (e *CSVError) Error() string {
	if e.Line > 0 {
		if e.Column != "" {
			return fmt.Sprintf("CSV %s error in file '%s' at line %d, column '%s': %v", e.Op, e.File, e.Line, e.Column, e.Err)
		}
		return fmt.Sprintf("CSV %s error in file '%s' at line %d: %v", e.Op, e.File, e.Line, e.Err)
	}
	return fmt.Sprintf("CSV %s error in file '%s': %v", e.Op, e.File, e.Err)
}

func (e *CSVError) Unwrap() error {
	return e.Err
}

// ValidationError represents input validation failures
type ValidationError struct {
	Field  string
	Value  string
	Reason string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation failed for field '%s' with value '%s': %s", e.Field, e.Value, e.Reason)
}

// validateCSVConfig validates CSV loading configuration
func validateCSVConfig(targetColumn string) error {
	if strings.TrimSpace(targetColumn) == "" {
		return &ValidationError{
			Field:  "target_column",
			Value:  targetColumn,
			Reason: "cannot be empty or whitespace-only",
		}
	}
	return nil
}

// isNAValue checks if a string represents a common NA/NaN value
func isNAValue(s string) bool {
	// Handle empty string as NA
	if s == "" {
		return true
	}

	normalized := strings.ToLower(strings.TrimSpace(s))

	// Handle whitespace-only strings as NA
	if normalized == "" {
		return true
	}

	for _, naValue := range DefaultNAValues {
		if normalized == naValue {
			return true
		}
	}
	return false
}

// ReadTrainingCSV reads and validates a CSV file according to RFC 4180 standards
// (including proper handling of quoted fields).
//
// The function performs the following validations:
// - File must have a .csv extension
// - File must be accessible for reading
// - CSV must have headers
// - Headers must not be empty strings
// - The specified target column must exist in the headers
// - Each row must have the same number of fields as the header
// - The target column must not contain empty values or NA/NaN values
// - There must be at least 2 data rows (excluding header)
// - Properly handles quoted fields (e.g., "Hello, world" is treated as a single field)
//
// Returns:
// - []string: The headers from data
// - [][]string: The parsed data if successful
// - int: target index
// - error: Any error encountered during reading or validation
func ReadTrainingCSV(filePath string, targetColumn string) ([]string, [][]string, int, error) {
	// Log the start of CSV reading operation
	logger.Debug( fmt.Sprintf("Starting CSV read operation: file=%s, target=%s", filePath, targetColumn))

	// Validate input parameters
	if err := validateCSVConfig(targetColumn); err != nil {
		logger.Error( fmt.Sprintf("CSV config validation failed: %v", err))
		return nil, nil, 0, err
	}

	// Validate file extension
	ext := filepath.Ext(filePath)
	if ext != ".csv" {
		logger.Error( fmt.Sprintf("Invalid file extension: %s (expected .csv)", ext))
		return nil, nil, 0, &ValidationError{
			Field:  "file_extension",
			Value:  ext,
			Reason: "expected .csv extension",
		}
	}

	// Open file
	logger.Debug( fmt.Sprintf("Opening CSV file: %s", filePath))
	file, err := os.Open(filePath)
	if err != nil {
		logger.Error( fmt.Sprintf("Failed to open file: %v", err))
		return nil, nil, 0, &CSVError{
			Op:   "open",
			File: filePath,
			Err:  err,
		}
	}
	defer file.Close()

	// Get file info for optimization
	fileInfo, err := file.Stat()
	if err != nil {
		logger.Error( fmt.Sprintf("Failed to get file info: %v", err))
		return nil, nil, 0, &CSVError{
			Op:   "stat",
			File: filePath,
			Err:  err,
		}
	}

	logger.Debug( fmt.Sprintf("File size: %d bytes", fileInfo.Size()))

	// Estimate number of rows for pre-allocation
	estimatedRows := int(fileInfo.Size() / 100) // Rough estimate: 100 bytes per row
	if estimatedRows < MinRequiredDataRows {
		estimatedRows = MinRequiredDataRows
	}

	// Process the CSV file with buffered reader for better performance
	reader := csv.NewReader(bufio.NewReader(file))

	// Configure the CSV reader
	reader.FieldsPerRecord = -1
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = false // Enforce strict quotes (RFC 4180 compliant)
	reader.Comma = DefaultDelimiter

	// Read headers
	logger.Debug( "Reading CSV headers")
	headers, err := reader.Read()
	if err != nil {
		logger.Error( fmt.Sprintf("Failed to read headers: %v", err))
		return nil, nil, 0, &CSVError{
			Op:   "read_headers",
			File: filePath,
			Err:  err,
		}
	}

	// Validate headers are not empty
	if len(headers) == 0 {
		logger.Error( "CSV file has no headers")
		return nil, nil, 0, &CSVError{
			Op:   "validate_headers",
			File: filePath,
			Err:  errors.New("CSV file has no headers"),
		}
	}

	logger.Debug( fmt.Sprintf("Found %d headers: %v", len(headers), headers))

	// Check for empty header fields, which could indicate quote parsing issues
	for i, h := range headers {
		if strings.TrimSpace(h) == "" {
			return nil, nil, 0, &CSVError{
				Op:     "validate_headers",
				File:   filePath,
				Column: fmt.Sprintf("column_%d", i+1),
				Err:    fmt.Errorf("empty header field at column %d, possible quote parsing issue", i+1),
			}
		}
	}

	// Find target column index
	logger.Debug( fmt.Sprintf("Looking for target column: %s", targetColumn))
	targetIdx := -1
	for i, h := range headers {
		if h == targetColumn {
			targetIdx = i
			break
		}
	}

	if targetIdx == -1 {
		logger.Error( fmt.Sprintf("Target column '%s' not found in headers: %v", targetColumn, headers))
		return nil, nil, 0, &ValidationError{
			Field:  "target_column",
			Value:  targetColumn,
			Reason: "not found in CSV headers",
		}
	}

	logger.Debug( fmt.Sprintf("Target column '%s' found at index %d", targetColumn, targetIdx))

	reader.FieldsPerRecord = len(headers)

	// Pre-allocate data slice with estimated size
	data := make([][]string, 0, estimatedRows)
	// lineNum starts at 1 to account for the header row already read
	lineNum := 1

	logger.Debug( "Starting to read data rows")

	for {
		lineNum++
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logger.Error( fmt.Sprintf("Failed to parse record at line %d: %v", lineNum, err))
			return nil, nil, 0, &CSVError{
				Op:   "parse_record",
				File: filePath,
				Line: lineNum,
				Err:  err,
			}
		}

		// Ensure the target column has a non-empty value
		// Also check for common NA/NaN values that might come from Python preprocessing
		if record[targetIdx] == "" || isNAValue(record[targetIdx]) {
			logger.Error( fmt.Sprintf("Empty or NA value in target column at line %d: '%s'", lineNum, record[targetIdx]))
			return nil, nil, 0, &CSVError{
				Op:     "validate_target",
				File:   filePath,
				Line:   lineNum,
				Column: targetColumn,
				Err:    fmt.Errorf("empty or NA value in target column"),
			}
		}

		data = append(data, record)
	}

	logger.Debug( fmt.Sprintf("Successfully read %d data rows", len(data)))

	// Check if we have enough data
	if len(data) < MinRequiredDataRows {
		logger.Error( fmt.Sprintf("Insufficient data rows: got %d, minimum required %d", len(data), MinRequiredDataRows))
		return nil, nil, 0, &CSVError{
			Op:   "validate_data_size",
			File: filePath,
			Err:  fmt.Errorf("insufficient data rows (minimum %d required, got %d)", MinRequiredDataRows, len(data)),
		}
	}

	logger.Info( fmt.Sprintf("Successfully loaded CSV: %d rows, %d columns, target='%s'", len(data), len(headers), targetColumn))
	return headers, data, targetIdx, nil
}

// ExtractRecordsAndTarget extracts feature columns and target column from parsed CSV data
//
// Parameters:
// - data: The CSV data as a slice of string slices (rows of columns)
// - headers: The CSV column headers
// - targetColumn: The name of the target column to extract
//
// Returns:
// - records: A slice where each element (row) represents one example, and contains all feature values for that example
// - targets: A slice where each element represents the target/class value for the corresponding example
// - featureHeaders: The names of the feature columns (excluding the target column)
// - error: Any error encountered during extraction, nil if successful
func ExtractRecordsAndTarget(data [][]string, headers []string, targetColumn string, targetIdx int) (records [][]string, targets []string, featureHeaders []string, err error) {
	logger.Debug( fmt.Sprintf("Extracting features and targets: %d rows, target='%s' at index %d", len(data), targetColumn, targetIdx))

	if targetIdx == -1 {
		logger.Error( fmt.Sprintf("Target column '%s' not found in headers", targetColumn))
		return nil, nil, nil, &ValidationError{
			Field:  "target_column",
			Value:  targetColumn,
			Reason: "not found in headers",
		}
	}

	// Validate data consistency
	if len(data) == 0 {
		logger.Error( "No data rows provided")
		return nil, nil, nil, &ValidationError{
			Field:  "data",
			Value:  "empty",
			Reason: "no data rows provided",
		}
	}

	// Create feature headers (all headers except the target column)
	featureHeaders = make([]string, 0, len(headers)-1)
	for i, h := range headers {
		if i != targetIdx {
			featureHeaders = append(featureHeaders, h)
		}
	}

	logger.Debug( fmt.Sprintf("Created %d feature headers: %v", len(featureHeaders), featureHeaders))

	// Pre-allocate slices with known size for better performance
	records = make([][]string, len(data))
	targets = make([]string, len(data))

	for i, row := range data {
		// Validate row length
		if len(row) != len(headers) {
			logger.Error( fmt.Sprintf("Row %d has incorrect length: expected %d, got %d", i, len(headers), len(row)))
			return nil, nil, nil, &ValidationError{
				Field:  "row_length",
				Value:  fmt.Sprintf("row_%d", i),
				Reason: fmt.Sprintf("expected %d columns, got %d", len(headers), len(row)),
			}
		}

		targets[i] = row[targetIdx]

		// Pre-allocate feature row
		record := make([]string, 0, len(row)-1)
		for j, val := range row {
			if j != targetIdx {
				record = append(record, val)
			}
		}
		records[i] = record
	}

	logger.Info( fmt.Sprintf("Successfully extracted %d records with %d features each", len(records), len(featureHeaders)))
	return records, targets, featureHeaders, nil
}
