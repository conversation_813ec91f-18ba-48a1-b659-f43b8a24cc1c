package utils

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
)

// Maximum allowed file size for metadata files (10MB)
const MaxMetadataFileSize = 10 * 1024 * 1024

// validatePath performs security validation on file paths
func validatePath(filePath string) error {
	// Check for path traversal attempts
	cleanPath := filepath.Clean(filePath)

	// More thorough check for path traversal
	if strings.Contains(cleanPath, "..") || strings.Contains(filePath, "..") {
		return fmt.Errorf("path traversal detected: %s", filePath)
	}

	// Check for absolute path traversal on Windows
	if len(cleanPath) > 1 && cleanPath[1] == ':' {
		// Allow Windows absolute paths but validate they're reasonable
		if !filepath.IsAbs(cleanPath) {
			return fmt.Errorf("invalid Windows path format: %s", filePath)
		}
	}

	return nil
}

/*
ValidateFeatureInfoFile validates that a given file path points to a valid metadata file.
This function performs comprehensive validation including:
- Path validation (non-empty, properly formatted)
- File extension validation (must be .yaml or .yml)
- File existence and accessibility checks
- File type validation (must be a regular file, not a directory or special file)
- Read permission verification
- File size validation (prevents extremely large files)

Parameters:
  - filePath: The path to the file to validate (can be relative or absolute)

Returns:
  - string: The validated file path (cleaned and normalized)
  - error: An error describing what validation failed, or nil if validation passed
*/
func ValidateFeatureInfoFile(filePath string) (string, error) {
	// Log the start of validation
	logger.Debug(fmt.Sprintf("Validating feature info file: %s", filePath))

	// Validate the input path is not empty or whitespace-only
	trimmedPath := strings.TrimSpace(filePath)
	if trimmedPath == "" {
		logger.Error("Empty file path provided")
		return "", errors.New("empty file path provided")
	}

	// Security validation
	if err := validatePath(trimmedPath); err != nil {
		logger.Error(fmt.Sprintf("Path validation failed: %v", err))
		return "", fmt.Errorf("path validation failed: %w", err)
	}

	// Validate file extension (case-insensitive)
	ext := strings.ToLower(filepath.Ext(trimmedPath))
	if !(ext == ".yaml" || ext == ".yml") {
		logger.Error(fmt.Sprintf("Invalid file extension: %s (expected .yaml or .yml)", ext))
		return "", fmt.Errorf("invalid file extension: %s, expected .yaml or .yml", ext)
	}

	// Check if file exists and get file information
	info, err := os.Stat(trimmedPath)
	if err != nil {
		// Check if the error is specifically because the file doesn't exist
		if os.IsNotExist(err) {
			logger.Error(fmt.Sprintf("File does not exist: %s", trimmedPath))
			return "", fmt.Errorf("file does not exist: %s", trimmedPath)
		}
		logger.Error(fmt.Sprintf("Error accessing file: %v", err))
		return "", fmt.Errorf("error accessing file: %w", err)
	}

	// Verify it's a regular file
	if !info.Mode().IsRegular() {
		logger.Error(fmt.Sprintf("Not a regular file: %s", trimmedPath))
		return "", fmt.Errorf("not a regular file: %s", trimmedPath)
	}

	// Check file size to prevent extremely large files
	if info.Size() > MaxMetadataFileSize {
		logger.Error(fmt.Sprintf("File too large: %d bytes (max %d bytes)", info.Size(), MaxMetadataFileSize))
		return "", fmt.Errorf("file too large: %d bytes (maximum %d bytes allowed)", info.Size(), MaxMetadataFileSize)
	}

	// Check if file is readable
	file, err := os.Open(trimmedPath)
	if err != nil {
		logger.Error( fmt.Sprintf("Cannot read file: %v", err))
		return "", fmt.Errorf("cannot read file: %w", err)
	}
	file.Close()

	logger.Debug( fmt.Sprintf("Successfully validated feature info file: %s (%d bytes)", trimmedPath, info.Size()))
	return trimmedPath, nil
}
