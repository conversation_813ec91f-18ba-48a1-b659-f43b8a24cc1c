package training

import (
	"fmt"

	"github.com/berrijam/mulberri/pkg/models"
)

// evaluateCategoricalSplit finds the best categorical split
func (c *C45Splitter[T]) evaluateCategoricalSplit(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	partitions := make(map[interface{}][]int)
	var errors MultiError

	// Group samples by their categorical values
	for _, idx := range dataset.GetIndices() {
		val, err := c.getCachedFeatureValue(dataset, idx, feature)
		if err != nil {
			errors.Add(fmt.<PERSON><PERSON><PERSON>("sample %d: %w", idx, err))
			continue
		}
		partitions[val] = append(partitions[val], idx)
	}

	if errors.HasErrors() {
		return nil, &SplitError{Op: "evaluate_categorical", Feature: feature.Name, Err: &errors}
	}

	// Check if all values are missing (nil)
	if len(partitions) == 1 {
		for key := range partitions {
			if key == nil {
				return nil, &SplitError{
					Op:      "evaluate_categorical",
					Feature: feature.Name,
					Err:     fmt.Errorf("all feature values are missing"),
				}
			}
		}
	}

	// Remove partitions that are too small (below minimum leaf size)
	for key, indices := range partitions {
		if len(indices) < c.config.MinSamplesLeaf {
			delete(partitions, key)
		}
	}

	// If we end up with 0 or 1 partition, no meaningful split is possible
	if len(partitions) <= 1 {
		return nil, nil
	}

	total := dataset.GetSize()
	splitSizes := make([]int, 0, len(partitions))
	weightedImp := 0.0

	// Calculate weighted impurity for each partition
	for _, indices := range partitions {
		targetDist := c.getTargetDistFromPool()

		var partErrors MultiError
		for _, idx := range indices {
			target, err := dataset.GetTarget(idx)
			if err != nil {
				partErrors.Add(fmt.Errorf("sample %d: %w", idx, err))
				continue
			}
			targetDist[target]++
		}

		if partErrors.HasErrors() {
			c.returnTargetDistToPool(targetDist)
			return nil, &SplitError{Op: "evaluate_categorical_partition", Feature: feature.Name, Err: &partErrors}
		}

		partSize := len(indices)
		splitSizes = append(splitSizes, partSize)
		partImp := c.calculateDistributionImpurity(targetDist, partSize)
		weightedImp += (float64(partSize) / float64(total)) * partImp

		c.returnTargetDistToPool(targetDist)
	}

	// Calculate information gain and gain ratio
	infoGain := baseImpurity - weightedImp
	splitInfo := calculateSplitInfo(splitSizes, total)

	// Avoid division by zero or very small values
	if splitInfo <= baseFloatTolerance {
		return nil, nil
	}

	return &SplitResult[T]{
		Feature:    feature,
		InfoGain:   infoGain,
		GainRatio:  infoGain / splitInfo,
		Partitions: partitions,
	}, nil
}
