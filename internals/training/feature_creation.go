// Package training implements decision tree training algorithms with enhanced error handling and validation.
package training

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TrainingError represents errors that occur during training operations
type TrainingError struct {
	Op     string
	Field  string
	Index  int
	Reason string
	Err    error
}

func (e *TrainingError) Error() string {
	if e.Index >= 0 {
		return fmt.Sprintf("training %s error at index %d for field '%s': %s", e.Op, e.Index, e.Field, e.Reason)
	}
	if e.Field != "" {
		return fmt.Sprintf("training %s error for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("training %s error: %s", e.Op, e.Reason)
}

func (e *TrainingError) Unwrap() error {
	return e.Err
}

// FeatureCreationConfig holds configuration for feature creation
type FeatureCreationConfig struct {
	MinNumericSamples   int     // Minimum samples needed to consider a column numeric
	NumericThreshold    float64 // Minimum ratio of numeric values to consider column numeric
	TrimWhitespace      bool    // Whether to trim whitespace from values
	HandleMissingValues bool    // Whether to handle missing values gracefully
	ValidateInputs      bool    // Whether to validate input parameters
}

// DefaultFeatureCreationConfig returns a sensible default configuration
func DefaultFeatureCreationConfig() *FeatureCreationConfig {
	return &FeatureCreationConfig{
		MinNumericSamples:   2,
		NumericThreshold:    0.8,
		TrimWhitespace:      true,
		HandleMissingValues: true,
		ValidateInputs:      true,
	}
}

// validateFeatureCreationInputs validates input parameters for CreateFeatures
func validateFeatureCreationInputs(rows [][]string, headers []string, config *FeatureCreationConfig) error {
	if !config.ValidateInputs {
		return nil
	}

	if headers == nil {
		return &TrainingError{
			Op:     "validate_inputs",
			Field:  "headers",
			Reason: "headers cannot be nil",
		}
	}

	if rows == nil {
		return &TrainingError{
			Op:     "validate_inputs",
			Field:  "rows",
			Reason: "rows cannot be nil",
		}
	}

	// Validate header names
	for i, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}
		if header == "" {
			return &TrainingError{
				Op:     "validate_inputs",
				Field:  "headers",
				Index:  i,
				Reason: "header cannot be empty or whitespace-only",
			}
		}
	}

	// Check for duplicate headers
	headerMap := make(map[string]int)
	for i, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}
		if prevIndex, exists := headerMap[header]; exists {
			return &TrainingError{
				Op:     "validate_inputs",
				Field:  "headers",
				Index:  i,
				Reason: fmt.Sprintf("duplicate header '%s' found at indices %d and %d", header, prevIndex, i),
			}
		}
		headerMap[header] = i
	}

	return nil
}

/*
CreateFeatures performs feature categorization by analyzing raw feature data
and creating structured Feature objects suitable for decision tree algorithms.
This function examines each column of data to determine its type (numeric or categorical)
and creates appropriate Feature objects with comprehensive error handling.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns) from ExtractFeatureAndTarget
  - headers: Names of the feature columns corresponding to each column in rows

Returns:
  - []*models.Feature: Array of Feature objects ready for machine learning algorithms
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeatures(rows [][]string, headers []string) ([]*models.Feature, error) {
	return CreateFeaturesWithConfig(rows, headers, DefaultFeatureCreationConfig())
}

/*
CreateFeaturesFromMetadata creates features using metadata information as the primary source.
This function prioritizes metadata definitions over automatic detection, providing
precise control over feature types and properties.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns)
  - headers: Names of the feature columns
  - metadataConfig: Metadata configuration mapping feature names to their definitions
  - config: Configuration for feature creation behavior (optional, uses defaults if nil)

Returns:
  - []*models.Feature: Array of Feature objects created from metadata
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeaturesFromMetadata(rows [][]string, headers []string, metadataConfig map[string]interface{}, config *FeatureCreationConfig) ([]*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	logger.Debug(  fmt.Sprintf("Creating features from metadata: %d headers, %d rows, %d metadata entries", len(headers), len(rows), len(metadataConfig)))

	// Validate inputs
	if err := validateFeatureCreationInputs(rows, headers, config); err != nil {
		logger.Error(  fmt.Sprintf("Input validation failed: %v", err))
		return nil, err
	}

	if len(headers) == 0 {
		logger.Debug(  "No headers provided, returning empty feature list")
		return []*models.Feature{}, nil
	}

	features := make([]*models.Feature, len(headers))
	for colIdx, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}

		// Extract column values safely
		columnValues, err := extractColumnValues(rows, colIdx, config)
		if err != nil {
			return nil, &TrainingError{
				Op:     "extract_column",
				Field:  "column_values",
				Index:  colIdx,
				Reason: fmt.Sprintf("failed to extract column values: %v", err),
				Err:    err,
			}
		}

		// Check if metadata exists for this feature
		if metadataInfo, exists := metadataConfig[header]; exists {
			logger.Debug(  fmt.Sprintf("Creating feature '%s' from metadata at index %d", header, colIdx))
			feature, err := createFeatureFromMetadata(header, colIdx, columnValues, metadataInfo, config)
			if err != nil {
				logger.Error(  fmt.Sprintf("Failed to create feature '%s' from metadata: %v", header, err))
				return nil, &TrainingError{
					Op:     "create_feature_from_metadata",
					Field:  "feature",
					Index:  colIdx,
					Reason: fmt.Sprintf("failed to create feature '%s' from metadata: %v", header, err),
					Err:    err,
				}
			}
			features[colIdx] = feature
		} else {
			// Fallback to automatic detection if no metadata found
			logger.Debug(  fmt.Sprintf("No metadata found for feature '%s', falling back to automatic detection", header))
			feature, err := AnalyzeAndCreateFeatureWithConfig(header, colIdx, columnValues, config)
			if err != nil {
				logger.Debug(  fmt.Sprintf("Failed to create feature '%s' with automatic detection: %v", header, err))
				return nil, &TrainingError{
					Op:     "create_feature_fallback",
					Field:  "feature",
					Index:  colIdx,
					Reason: fmt.Sprintf("failed to create feature '%s' with automatic detection: %v", header, err),
					Err:    err,
				}
			}
			features[colIdx] = feature
		}
	}

	logger.Debug(  fmt.Sprintf("Successfully created %d features from metadata", len(features)))
	return features, nil
}

/*
CreateFeaturesWithConfig performs feature creation with custom configuration.
This allows fine-tuning of the feature creation process for specific use cases.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns)
  - headers: Names of the feature columns
  - config: Configuration for feature creation behavior

Returns:
  - []*models.Feature: Array of Feature objects
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeaturesWithConfig(rows [][]string, headers []string, config *FeatureCreationConfig) ([]*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	logger.Debug( fmt.Sprintf("Creating features: %d headers, %d rows", len(headers), len(rows)))

	// Validate inputs
	if err := validateFeatureCreationInputs(rows, headers, config); err != nil {
		logger.Debug( fmt.Sprintf("Input validation failed: %v", err))
		return nil, err
	}

	if len(headers) == 0 {
		logger.Debug( "No headers provided, returning empty feature list")
		return []*models.Feature{}, nil
	}

	features := make([]*models.Feature, len(headers))
	for colIdx, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}

		// Extract column values safely
		columnValues, err := extractColumnValues(rows, colIdx, config)
		if err != nil {
			return nil, &TrainingError{
				Op:     "extract_column",
				Field:  "column_values",
				Index:  colIdx,
				Reason: fmt.Sprintf("failed to extract column values: %v", err),
				Err:    err,
			}
		}

		// Create feature with error handling
		logger.Debug( fmt.Sprintf("Creating feature '%s' at index %d", header, colIdx))
		feature, err := AnalyzeAndCreateFeatureWithConfig(header, colIdx, columnValues, config)
		if err != nil {
			logger.Error( fmt.Sprintf("Failed to create feature '%s': %v", header, err))
			return nil, &TrainingError{
				Op:     "create_feature",
				Field:  "feature",
				Index:  colIdx,
				Reason: fmt.Sprintf("failed to create feature '%s': %v", header, err),
				Err:    err,
			}
		}

		logger.Debug( fmt.Sprintf("Successfully created feature '%s' (type: %v)", header, feature.Type))
		features[colIdx] = feature
	}

	logger.Info( fmt.Sprintf("Successfully created %d features", len(features)))
	return features, nil
}

// extractColumnValues safely extracts values from a specific column
func extractColumnValues(rows [][]string, colIdx int, config *FeatureCreationConfig) ([]string, error) {
	if colIdx < 0 {
		return nil, fmt.Errorf("column index cannot be negative: %d", colIdx)
	}

	columnValues := make([]string, len(rows))
	for rowIdx, row := range rows {
		var value string
		if colIdx < len(row) {
			value = row[colIdx]
			if config.TrimWhitespace {
				value = strings.TrimSpace(value)
			}
		} else if !config.HandleMissingValues {
			return nil, fmt.Errorf("row %d has %d columns, but column %d was requested", rowIdx, len(row), colIdx)
		}
		// If HandleMissingValues is true, missing values become empty strings
		columnValues[rowIdx] = value
	}

	return columnValues, nil
}

/*
AnalyzeAndCreateFeature analyzes a single column of data and creates
the appropriate Feature object based on the detected data type.
Uses default configuration for backward compatibility.

Parameters:
  - name: Name of the feature (column header)
  - index: Column index in the original dataset
  - values: All values in this column as strings

Returns:
  - *models.Feature: Constructed feature object with appropriate type
  - error: Error if feature creation fails
*/
func AnalyzeAndCreateFeature(name string, index int, values []string) (*models.Feature, error) {
	return AnalyzeAndCreateFeatureWithConfig(name, index, values, DefaultFeatureCreationConfig())
}

/*
AnalyzeAndCreateFeatureWithConfig analyzes a single column of data and creates
the appropriate Feature object with custom configuration.

Parameters:
  - name: Name of the feature (column header)
  - index: Column index in the original dataset
  - values: All values in this column as strings
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Constructed feature object with appropriate type
  - error: Error if feature creation fails
*/
func AnalyzeAndCreateFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	// Validate inputs - always validate name and index as these are critical
	trimmedName := name
	if config.TrimWhitespace {
		trimmedName = strings.TrimSpace(name)
	}
	if trimmedName == "" {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	if index < 0 {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "index",
			Reason: fmt.Sprintf("feature index cannot be negative: %d", index),
		}
	}

	if config.ValidateInputs && values == nil {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "values",
			Reason: "values cannot be nil",
		}
	}

	featureType, err := DetermineFeatureTypeWithConfig(values, config)
	if err != nil {
		return nil, &TrainingError{
			Op:     "determine_type",
			Field:  "feature_type",
			Reason: fmt.Sprintf("failed to determine feature type: %v", err),
			Err:    err,
		}
	}

	switch featureType {
	case models.NumericFeature:
		return CreateNumericFeatureWithConfig(trimmedName, index, values, config)
	case models.CategoricalFeature:
		return CreateCategoricalFeatureWithConfig(trimmedName, index, values, config)
	default:
		// Default to categorical for unknown types
		return CreateCategoricalFeatureWithConfig(trimmedName, index, values, config)
	}
}

/*
CreateNumericFeatureWithConfig creates a Feature object for numeric data with error handling.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column (will be converted to numbers)
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Numeric feature with min/max range set
  - error: Error if feature creation fails
*/
func CreateNumericFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create feature with validation
	feature, err := models.NewFeature(name, models.NumericFeature, index)
	if err != nil {
		return nil, &TrainingError{
			Op:     "create_numeric_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create numeric feature: %v", err),
			Err:    err,
		}
	}

	var numValues []float64
	var parseErrors []string

	for i, val := range values {
		// Skip empty values
		if val == "" {
			continue
		}

		if num, parseErr := strconv.ParseFloat(val, 64); parseErr == nil {
			numValues = append(numValues, num)
		} else if config.ValidateInputs {
			parseErrors = append(parseErrors, fmt.Sprintf("row %d: '%s'", i, val))
		}
	}

	// Set range if we have numeric values
	if len(numValues) > 0 {
		min, max := numValues[0], numValues[0]
		for _, val := range numValues[1:] {
			if val < min {
				min = val
			}
			if val > max {
				max = val
			}
		}

		// Only set range if min != max to avoid validation error
		if min != max {
			if err := feature.SetRange(min, max); err != nil {
				return nil, &TrainingError{
					Op:     "set_range",
					Field:  "numeric_range",
					Reason: fmt.Sprintf("failed to set numeric range [%f, %f]: %v", min, max, err),
					Err:    err,
				}
			}
		} else {
			// For single value, set a small range
			epsilon := 0.001
			if err := feature.SetRange(min-epsilon, max+epsilon); err != nil {
				return nil, &TrainingError{
					Op:     "set_range",
					Field:  "numeric_range",
					Reason: fmt.Sprintf("failed to set single-value range: %v", err),
					Err:    err,
				}
			}
		}
	} else if config.ValidateInputs && len(values) > 0 {
		return nil, &TrainingError{
			Op:     "parse_numeric_values",
			Field:  "values",
			Reason: fmt.Sprintf("no valid numeric values found, parse errors: %v", parseErrors),
		}
	}

	return feature, nil
}

/*
CreateCategoricalFeatureWithConfig creates a Feature object for categorical data with error handling.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Categorical feature with unique values populated
  - error: Error if feature creation fails
*/
func CreateCategoricalFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create feature with validation
	feature, err := models.NewFeature(name, models.CategoricalFeature, index)
	if err != nil {
		return nil, &TrainingError{
			Op:     "create_categorical_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create categorical feature: %v", err),
			Err:    err,
		}
	}

	// Collect unique non-empty values
	uniqueValues := make(map[string]bool)
	for _, val := range values {
		if config.TrimWhitespace {
			val = strings.TrimSpace(val)
		}
		// Skip empty values as they're not allowed by the models package
		if val != "" {
			uniqueValues[val] = true
		}
	}

	// Add unique values to feature
	for val := range uniqueValues {
		// Try the new AddCategoricalValue method first
		added, err := feature.AddCategoricalValue(val)
		if err != nil {
			// Fall back to legacy AddValue method
			_, legacyErr := feature.AddValue(val)
			if legacyErr != nil {
				return nil, &TrainingError{
					Op:     "add_categorical_value",
					Field:  "value",
					Reason: fmt.Sprintf("failed to add categorical value '%s': %v", val, legacyErr),
					Err:    legacyErr,
				}
			}
		} else if !added && config.ValidateInputs {
			// This is expected for duplicates, so only log in debug mode
			// Could add logging here if needed
		}
	}

	return feature, nil
}

/*
DetermineFeatureType analyzes a column of values to determine whether
it should be treated as numeric or categorical.
Uses default configuration for backward compatibility.

Parameters:
  - values: All string values from the column to analyze

Returns:
  - models.FeatureType: Either NumericFeature or CategoricalFeature
  - error: Error if analysis fails (only with validation enabled)
*/
func DetermineFeatureType(values []string) (models.FeatureType, error) {
	featureType, err := DetermineFeatureTypeWithConfig(values, DefaultFeatureCreationConfig())
	return featureType, err
}

/*
DetermineFeatureTypeWithConfig analyzes a column of values with custom configuration.

Parameters:
  - values: All string values from the column to analyze
  - config: Configuration for type determination

Returns:
  - models.FeatureType: Either NumericFeature or CategoricalFeature
  - error: Error if analysis fails
*/
func DetermineFeatureTypeWithConfig(values []string, config *FeatureCreationConfig) (models.FeatureType, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	if config.ValidateInputs && values == nil {
		return models.CategoricalFeature, &TrainingError{
			Op:     "determine_type",
			Field:  "values",
			Reason: "values cannot be nil",
		}
	}

	if len(values) == 0 {
		return models.CategoricalFeature, nil
	}

	var numericCount, totalCount int

	for _, val := range values {
		if config.TrimWhitespace {
			val = strings.TrimSpace(val)
		}

		if val == "" {
			continue // Skip empty values
		}

		totalCount++
		if _, err := strconv.ParseFloat(val, 64); err == nil {
			numericCount++
		}
	}

	// If no non-empty values, default to categorical
	if totalCount == 0 {
		return models.CategoricalFeature, nil
	}

	// Check if we have enough numeric values
	if numericCount >= config.MinNumericSamples {
		numericRatio := float64(numericCount) / float64(totalCount)
		if numericRatio >= config.NumericThreshold {
			return models.NumericFeature, nil
		}
	}

	return models.CategoricalFeature, nil
}

/*
createFeatureFromMetadata creates a Feature object using metadata information.
This function interprets metadata to determine feature type and properties.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column
  - metadataInfo: Metadata information for this feature
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Constructed feature object based on metadata
  - error: Error if feature creation fails
*/
func createFeatureFromMetadata(name string, index int, values []string, metadataInfo interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Convert metadata info to map for easier access
	metadataMap, ok := metadataInfo.(map[string]interface{})
	if !ok {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "metadata_info",
			Index:  index,
			Reason: fmt.Sprintf("metadata for feature '%s' is not a valid map", name),
		}
	}

	// Extract feature type from metadata
	featureTypeRaw, exists := metadataMap["type"]
	if !exists {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "type",
			Index:  index,
			Reason: fmt.Sprintf("metadata for feature '%s' missing 'type' field", name),
		}
	}

	featureTypeStr, ok := featureTypeRaw.(string)
	if !ok {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "type",
			Index:  index,
			Reason: fmt.Sprintf("metadata type for feature '%s' is not a string", name),
		}
	}

	// Convert metadata type to models.FeatureType
	var featureType models.FeatureType
	switch featureTypeStr {
	case "numeric":
		featureType = models.NumericFeature
	case "nominal":
		featureType = models.CategoricalFeature
	default:
		logger.Warn(fmt.Sprintf("Unknown metadata type '%s' for feature '%s', defaulting to categorical", featureTypeStr, name))
		featureType = models.CategoricalFeature
	}

	// Create feature based on type
	switch featureType {
	case models.NumericFeature:
		return createNumericFeatureFromMetadata(name, index, values, metadataMap, config)
	case models.CategoricalFeature:
		return createCategoricalFeatureFromMetadata(name, index, values, metadataMap, config)
	default:
		// Default to categorical for unknown types
		return createCategoricalFeatureFromMetadata(name, index, values, metadataMap, config)
	}
}

/*
createNumericFeatureFromMetadata creates a numeric feature using metadata constraints.
*/
func createNumericFeatureFromMetadata(name string, index int, values []string, metadataMap map[string]interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create the basic numeric feature first
	feature, err := CreateNumericFeatureWithConfig(name, index, values, config)
	if err != nil {
		return nil, err
	}

	// Apply metadata constraints if available
	if minVal, exists := metadataMap["min"]; exists {
		if minFloat, ok := minVal.(float64); ok {
			feature.Min = minFloat
		}
	}

	if maxVal, exists := metadataMap["max"]; exists {
		if maxFloat, ok := maxVal.(float64); ok {
			feature.Max = maxFloat
		}
	}

	logger.Debug(fmt.Sprintf("Created numeric feature '%s' from metadata with range [%.2f, %.2f]", name, feature.Min, feature.Max))
	return feature, nil
}

/*
createCategoricalFeatureFromMetadata creates a categorical feature using metadata values.
*/
func createCategoricalFeatureFromMetadata(name string, index int, values []string, metadataMap map[string]interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create the basic categorical feature first
	feature, err := CreateCategoricalFeatureWithConfig(name, index, values, config)
	if err != nil {
		return nil, err
	}

	// Apply metadata values if available
	if valuesRaw, exists := metadataMap["values"]; exists {
		if valuesSlice, ok := valuesRaw.([]interface{}); ok {
			// Convert interface{} slice to string slice
			metadataValues := make([]string, len(valuesSlice))
			for i, v := range valuesSlice {
				if str, ok := v.(string); ok {
					metadataValues[i] = str
				} else {
					metadataValues[i] = fmt.Sprintf("%v", v)
				}
			}
			feature.CategoricalValues = metadataValues
			logger.Debug(fmt.Sprintf("Created categorical feature '%s' from metadata with %d predefined values", name, len(metadataValues)))
		}
	}

	return feature, nil
}

// Backward compatibility wrappers for the old API

// createNumericFeature creates a numeric feature using default configuration
func CreateNumericFeature(name string, index int, values []string) *models.Feature {
	feature, err := CreateNumericFeatureWithConfig(name, index, values, DefaultFeatureCreationConfig())
	if err != nil {
		// For backward compatibility, we'll create a basic feature if enhanced creation fails
		basicFeature, basicErr := models.NewFeature(name, models.NumericFeature, index)
		if basicErr != nil {
			// This should not happen in practice, but for safety
			return &models.Feature{
				Name:         name,
				Type:         models.NumericFeature,
				ColumnNumber: index,
			}
		}
		return basicFeature
	}
	return feature
}

// createCategoricalFeature creates a categorical feature using default configuration
func CreateCategoricalFeature(name string, index int, values []string) *models.Feature {
	feature, err := CreateCategoricalFeatureWithConfig(name, index, values, DefaultFeatureCreationConfig())
	if err != nil {
		// For backward compatibility, we'll create a basic feature if enhanced creation fails
		basicFeature, basicErr := models.NewFeature(name, models.CategoricalFeature, index)
		if basicErr != nil {
			// This should not happen in practice, but for safety
			return &models.Feature{
				Name:         name,
				Type:         models.CategoricalFeature,
				ColumnNumber: index,
			}
		}

		// Add values using legacy method for backward compatibility, skip empty values
		uniqueValues := make(map[string]bool)
		for _, val := range values {
			val = strings.TrimSpace(val)
			if val != "" { // Skip empty values
				uniqueValues[val] = true
			}
		}
		for val := range uniqueValues {
			basicFeature.AddValue(val)
		}

		return basicFeature
	}
	return feature
}
