package training

import (
	"fmt"
	"log"
	"sort"

	"github.com/berrijam/mulberri/pkg/models"
)

// evaluateNumericSplit finds the best threshold for a numeric feature
func (c *C45Splitter[T]) evaluateNumericSplit(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	// Check if we have pre-sorted data for this feature
	featureID := feature.Name
	if sortedData, found := c.numericCache.Get(featureID); found && sortedData.Sorted {
		// Filter values for current dataset indices
		values := c.filterSortedValues(sortedData.Values, dataset.GetIndices())
		if len(values) < c.config.MinSamplesSplit {
			return nil, nil
		}
		return c.findBestThreshold(values, baseImpurity, feature)
	}

	// Collect and sort values (first time for this feature)
	values, err := c.collectNumericValues(dataset, feature)
	if err != nil {
		return nil, &SplitError{Op: "collect_numeric_values", Feature: feature.Name, Err: err}
	}
	if len(values) < c.config.MinSamplesSplit {
		return nil, nil
	}

	sort.Slice(values, func(i, j int) bool {
		return values[i].Value < values[j].Value
	})

	// Cache the sorted data for future use
	c.numericCache.Set(featureID, &PreSortedFeatureData[T]{
		Feature: feature,
		Values:  values,
		Sorted:  true,
	})

	return c.findBestThreshold(values, baseImpurity, feature)
}

// filterSortedValues filters pre-sorted values based on current dataset indices
func (c *C45Splitter[T]) filterSortedValues(allValues []numericValue[T], indices []int) []numericValue[T] {
	// Create a set of indices for O(1) lookup
	indexSet := make(map[int]bool, len(indices))
	for _, idx := range indices {
		indexSet[idx] = true
	}

	result := make([]numericValue[T], 0, len(indices))
	for _, value := range allValues {
		if indexSet[value.Index] {
			result = append(result, value)
		}
	}
	return result
}

// collectNumericValues gathers numeric values from dataset
func (c *C45Splitter[T]) collectNumericValues(dataset Dataset[T], feature *models.Feature) ([]numericValue[T], error) {
	indices := dataset.GetIndices()
	values := make([]numericValue[T], 0, len(indices))
	var errors MultiError

	for _, idx := range indices {
		raw, err := c.getCachedFeatureValue(dataset, idx, feature)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", idx, err))
			continue
		}

		val, err := c.convertToFloat64(raw)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d conversion: %w", idx, err))
			continue
		}

		target, err := dataset.GetTarget(idx)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d target: %w", idx, err))
			continue
		}

		values = append(values, numericValue[T]{Value: val, Index: idx, Target: target})
	}

	if errors.HasErrors() {
		return nil, &errors
	}

	return values, nil
}

// findBestThreshold finds the optimal threshold for numeric splitting
func (c *C45Splitter[T]) findBestThreshold(values []numericValue[T], baseImpurity float64, feature *models.Feature) (*SplitResult[T], error) {
	best := &SplitResult[T]{GainRatio: -1}

	// Use pools for distribution maps
	leftDist := c.getTargetDistFromPool()
	rightDist := c.getTargetDistFromPool()
	defer func() {
		c.returnTargetDistToPool(leftDist)
		c.returnTargetDistToPool(rightDist)
	}()

	// Initialize right distribution with all values
	for _, v := range values {
		rightDist[v.Target]++
	}

	total := len(values)
	for i := 0; i < total-1; i++ {
		// Move current value from right to left
		leftDist[values[i].Target]++
		rightDist[values[i].Target]--
		if rightDist[values[i].Target] == 0 {
			delete(rightDist, values[i].Target)
		}

		// Skip if next value is the same (no meaningful threshold)
		if floatEqual(values[i].Value, values[i+1].Value) {
			continue
		}

		leftSize := i + 1
		rightSize := total - leftSize

		// Check minimum samples constraints
		if leftSize < c.config.MinSamplesLeaf || rightSize < c.config.MinSamplesLeaf {
			continue
		}

		threshold, err := safeThreshold(values[i].Value, values[i+1].Value)
		if err != nil {
			if c.config.EnableLogging {
				log.Printf("Warning: threshold calculation failed for feature %s: %v", feature.Name, err)
			}
			continue
		}

		leftImp := c.calculateDistributionImpurity(leftDist, leftSize)
		rightImp := c.calculateDistributionImpurity(rightDist, rightSize)

		weightedImp := (float64(leftSize)/float64(total))*leftImp + (float64(rightSize)/float64(total))*rightImp
		infoGain := baseImpurity - weightedImp
		splitInfo := calculateSplitInfo([]int{leftSize, rightSize}, total)

		if splitInfo <= baseFloatTolerance {
			continue
		}

		gainRatio := infoGain / splitInfo
		if gainRatio > best.GainRatio {
			best = &SplitResult[T]{
				Feature:      feature,
				Threshold:    threshold,
				InfoGain:     infoGain,
				GainRatio:    gainRatio,
				LeftIndices:  getIndices(values, 0, i),
				RightIndices: getIndices(values, i+1, total-1),
			}
		}
	}

	if best.GainRatio <= 0 {
		return nil, nil
	}
	return best, nil
}

// getIndices extracts indices from a slice of numeric values
func getIndices[T comparable](values []numericValue[T], start, end int) []int {
	if start < 0 || end >= len(values) || start > end {
		return nil
	}

	indices := make([]int, 0, end-start+1)
	for i := start; i <= end; i++ {
		indices = append(indices, values[i].Index)
	}
	return indices
}