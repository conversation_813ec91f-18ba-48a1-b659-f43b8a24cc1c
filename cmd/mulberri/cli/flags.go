package cli

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"
)

// ParseFlags processes command-line arguments and returns a validated Config structure.
// It enforces required parameters for both training and prediction operations and
// supports advanced decision tree configuration options.
//
// The function handles:
//   - Core command flags (command, input, output, etc.)
//   - Decision tree parameters (max-depth, min-samples, criterion)
//   - Special flags (help, version, verbose)
//   - Comprehensive validation of all parameters
//
// Returns a fully validated Config or an error with specific context.
func ParseFlags() (*Config, error) {
	config := &Config{}

	// Core command flags
	flag.StringVar(&config.Command, "c", "", "Specify the command (train or predict)")
	flag.StringVar(&config.InputFile, "i", "", "Path to the input CSV file")
	flag.StringVar(&config.TargetCol, "t", "", "Name of the target column (for training only)")
	flag.StringVar(&config.OutputFile, "o", "", "Path to save the output (trained model or predictions)")
	flag.StringVar(&config.ModelFile, "m", "", "Path to the trained model file (for prediction only)")
	flag.StringVar(&config.FeatureInfoFile, "f", "", "Path to the metadata file containing feature descriptions")
	flag.StringVar(&config.LogConfigPath, "log-config", "", "Path to the logger configuration file")

	// Decision tree parameters with defaults
	var maxDepth = flag.Int("max-depth", 10, "Maximum depth of the decision tree")
	var minSamplesSplit = flag.Int("min-samples", 2, "Minimum samples required to split a node")
	var minSamplesLeaf = flag.Int("min-leaf", 1, "Minimum samples required at a leaf node")
	var criterion = flag.String("criterion", "entropy", "Splitting criterion (gini, entropy, mse)")

	// CLI options
	var verbose = flag.Bool("verbose", false, "Enable verbose output")
	var help = flag.Bool("help", false, "Show help message")
	var version = flag.Bool("version", false, "Show version information")

	// Custom usage function
	flag.Usage = func() {
		config.ShowHelp()
	}

	flag.Parse()

	// Handle special flags first
	if *help {
		config.ShowHelp()
		os.Exit(0)
	}

	if *version {
		config.ShowVersion()
		os.Exit(0)
	}

	// Set parsed values
	config.MaxDepth = *maxDepth
	config.MinSamplesSplit = *minSamplesSplit
	config.MinSamplesLeaf = *minSamplesLeaf
	config.Criterion = strings.ToLower(strings.TrimSpace(*criterion))
	config.Verbose = *verbose

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// ParseFlagsWithOptions creates a Config using functional options and command-line arguments.
// This provides an alternative approach for programmatic configuration.
func ParseFlagsWithOptions(baseOptions ...CLIOption) (*Config, error) {
	// Create base config with options
	config, err := NewConfig(baseOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to create base config: %w", err)
	}

	// Override with command-line arguments
	parsedConfig, err := ParseFlags()
	if err != nil {
		return nil, err
	}

	// Merge configurations (command-line takes precedence)
	if err := mergeConfigs(config, parsedConfig); err != nil {
		return nil, fmt.Errorf("failed to merge configurations: %w", err)
	}

	return config, nil
}

// mergeConfigs merges command-line parsed config into base config.
// Command-line values take precedence over base config values.
func mergeConfigs(base, override *Config) error {
	if override.Command != "" {
		base.Command = override.Command
	}
	if override.InputFile != "" {
		base.InputFile = override.InputFile
	}
	if override.TargetCol != "" {
		base.TargetCol = override.TargetCol
	}
	if override.OutputFile != "" {
		base.OutputFile = override.OutputFile
	}
	if override.ModelFile != "" {
		base.ModelFile = override.ModelFile
	}
	if override.FeatureInfoFile != "" {
		base.FeatureInfoFile = override.FeatureInfoFile
	}
	if override.LogConfigPath != "" {
		base.LogConfigPath = override.LogConfigPath
	}

	// Decision tree parameters (only override if explicitly set and different from defaults)
	if override.MaxDepth != 0 && override.MaxDepth != 10 { // Check for non-zero and non-default
		base.MaxDepth = override.MaxDepth
	}
	if override.MinSamplesSplit != 0 && override.MinSamplesSplit != 2 { // Check for non-zero and non-default
		base.MinSamplesSplit = override.MinSamplesSplit
	}
	if override.MinSamplesLeaf != 0 && override.MinSamplesLeaf != 1 { // Check for non-zero and non-default
		base.MinSamplesLeaf = override.MinSamplesLeaf
	}
	if override.Criterion != "" && override.Criterion != "gini" { // Check for non-empty and non-default
		base.Criterion = override.Criterion
	}

	// CLI options
	if override.Verbose {
		base.Verbose = override.Verbose
	}

	return nil
}

// ParseEnvironmentVariables parses environment variables for configuration.
// This provides an alternative configuration method for containerized environments.
func ParseEnvironmentVariables() (*Config, error) {
	config := &Config{}

	// Core parameters
	config.Command = os.Getenv("MULBERRI_COMMAND")
	config.InputFile = os.Getenv("MULBERRI_INPUT_FILE")
	config.TargetCol = os.Getenv("MULBERRI_TARGET_COL")
	config.OutputFile = os.Getenv("MULBERRI_OUTPUT_FILE")
	config.ModelFile = os.Getenv("MULBERRI_MODEL_FILE")
	config.FeatureInfoFile = os.Getenv("MULBERRI_FEATURE_INFO_FILE")
	config.LogConfigPath = os.Getenv("MULBERRI_LOG_CONFIG_PATH")

	// Decision tree parameters with defaults
	if maxDepthStr := os.Getenv("MULBERRI_MAX_DEPTH"); maxDepthStr != "" {
		if maxDepth, err := strconv.Atoi(maxDepthStr); err == nil {
			config.MaxDepth = maxDepth
		} else {
			return nil, &CLIError{
				Op:  "parsing",
				Err: fmt.Errorf("invalid MULBERRI_MAX_DEPTH: %s", maxDepthStr),
			}
		}
	} else {
		config.MaxDepth = 10
	}

	if minSamplesStr := os.Getenv("MULBERRI_MIN_SAMPLES_SPLIT"); minSamplesStr != "" {
		if minSamples, err := strconv.Atoi(minSamplesStr); err == nil {
			config.MinSamplesSplit = minSamples
		} else {
			return nil, &CLIError{
				Op:  "parsing",
				Err: fmt.Errorf("invalid MULBERRI_MIN_SAMPLES_SPLIT: %s", minSamplesStr),
			}
		}
	} else {
		config.MinSamplesSplit = 2
	}

	if minLeafStr := os.Getenv("MULBERRI_MIN_SAMPLES_LEAF"); minLeafStr != "" {
		if minLeaf, err := strconv.Atoi(minLeafStr); err == nil {
			config.MinSamplesLeaf = minLeaf
		} else {
			return nil, &CLIError{
				Op:  "parsing",
				Err: fmt.Errorf("invalid MULBERRI_MIN_SAMPLES_LEAF: %s", minLeafStr),
			}
		}
	} else {
		config.MinSamplesLeaf = 1
	}

	if criterion := os.Getenv("MULBERRI_CRITERION"); criterion != "" {
		config.Criterion = strings.ToLower(strings.TrimSpace(criterion))
	} else {
		config.Criterion = "entropy"
	}

	// CLI options
	if verboseStr := os.Getenv("MULBERRI_VERBOSE"); verboseStr != "" {
		if verbose, err := strconv.ParseBool(verboseStr); err == nil {
			config.Verbose = verbose
		}
	}

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("environment configuration validation failed: %w", err)
	}

	return config, nil
}

// ValidateArgs performs additional validation on command-line arguments.
// This can be used to check for conflicting flags or invalid combinations.
func ValidateArgs() error {
	args := flag.Args()

	// Check for unexpected positional arguments
	if len(args) > 0 {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("unexpected positional arguments: %v", args),
		}
	}

	return nil
}

// GetConfigFromMultipleSources attempts to load configuration from multiple sources
// in order of precedence: command-line flags, environment variables, defaults.
func GetConfigFromMultipleSources(baseOptions ...CLIOption) (*Config, error) {
	// Start with base options
	config, err := NewConfig(baseOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to create base config: %w", err)
	}

	// Try to load from environment variables
	envConfig, err := ParseEnvironmentVariables()
	if err == nil {
		if err := mergeConfigs(config, envConfig); err != nil {
			return nil, fmt.Errorf("failed to merge environment config: %w", err)
		}
	}

	// Command-line flags take highest precedence
	cliConfig, err := ParseFlags()
	if err != nil {
		return nil, err
	}

	if err := mergeConfigs(config, cliConfig); err != nil {
		return nil, fmt.Errorf("failed to merge CLI config: %w", err)
	}

	// Final validation
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("final configuration validation failed: %w", err)
	}

	return config, nil
}
