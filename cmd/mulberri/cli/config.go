// Package cli provides a command-line interface for Mulberri decision tree operations.
//
// This package offers:
//   - Training decision trees from CSV data with configurable parameters
//   - Prediction using trained models with batch processing support
//   - Comprehensive input validation and error handling
//   - Flexible configuration with functional options pattern
//   - Support for metadata files and feature descriptions
//
// Basic usage:
//
//	config, err := cli.ParseFlags()
//	if err != nil {
//	    log.Fatal(err)
//	}
//
//	if err := config.Validate(); err != nil {
//	    log.Fatal(err)
//	}
//
//	switch config.Command {
//	case "train":
//	    // Handle training logic
//	case "predict":
//	    // Handle prediction logic
//	}
//
// For advanced configuration and custom options, see the examples
// in the package documentation.
package cli

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// Config holds command-line parameters for Mulberri's operations.
// It contains fields for both training and prediction workflows with
// comprehensive validation and default values.
type Config struct {
	// Core command parameters
	Command         string // Operation to perform: "train" or "predict"
	InputFile       string // Path to the input CSV data file
	TargetCol       string // Name of the target column for training
	OutputFile      string // Path to save model or predictions
	ModelFile       string // Path to the trained model file for prediction
	FeatureInfoFile string // Path to the metadata file containing feature descriptions
	LogConfigPath   string // Path to the logger configuration file

	// Decision tree parameters
	MaxDepth        int    // Maximum depth of the decision tree (default: 10)
	MinSamplesSplit int    // Minimum samples required to split a node (default: 2)
	MinSamplesLeaf  int    // Minimum samples required at a leaf node (default: 1)
	Criterion       string // Splitting criterion: "gini", "entropy", "mse" (default: "entropy")

	// CLI options
	Verbose bool // Enable verbose output
	Help    bool // Show help message
	Version bool // Show version information
}

// CLIError represents errors that occur during CLI operations.
// It provides structured error context for better debugging and user feedback.
type CLIError struct {
	Op      string // Operation that failed (e.g., "validation", "parsing")
	Command string // Command being executed when error occurred
	Flag    string // Specific flag that caused the error (if applicable)
	Err     error  // Underlying error
}

// Error implements the error interface for CLIError.
func (e *CLIError) Error() string {
	if e.Flag != "" {
		return fmt.Sprintf("CLI %s failed for command '%s' flag '%s': %v",
			e.Op, e.Command, e.Flag, e.Err)
	}
	if e.Command != "" {
		return fmt.Sprintf("CLI %s failed for command '%s': %v",
			e.Op, e.Command, e.Err)
	}
	return fmt.Sprintf("CLI %s failed: %v", e.Op, e.Err)
}

// Unwrap returns the underlying error for error chain inspection.
func (e *CLIError) Unwrap() error {
	return e.Err
}

// CLIOption represents a functional option for configuring CLI parameters.
type CLIOption func(*Config) error

// WithMaxDepth sets the maximum depth for the decision tree.
// Depth must be a positive integer.
func WithMaxDepth(depth int) CLIOption {
	return func(config *Config) error {
		if depth <= 0 {
			return &CLIError{
				Op:  "validation",
				Err: fmt.Errorf("max depth must be positive: %d", depth),
			}
		}
		config.MaxDepth = depth
		return nil
	}
}

// WithMinSamplesSplit sets the minimum samples required to split a node.
// Must be at least 2.
func WithMinSamplesSplit(samples int) CLIOption {
	return func(config *Config) error {
		if samples < 2 {
			return &CLIError{
				Op:  "validation",
				Err: fmt.Errorf("min samples split must be >= 2: %d", samples),
			}
		}
		config.MinSamplesSplit = samples
		return nil
	}
}

// WithMinSamplesLeaf sets the minimum samples required at a leaf node.
// Must be at least 1.
func WithMinSamplesLeaf(samples int) CLIOption {
	return func(config *Config) error {
		if samples < 1 {
			return &CLIError{
				Op:  "validation",
				Err: fmt.Errorf("min samples leaf must be >= 1: %d", samples),
			}
		}
		config.MinSamplesLeaf = samples
		return nil
	}
}

// WithCriterion sets the splitting criterion for the decision tree.
// Valid values are "gini", "entropy", and "mse".
func WithCriterion(criterion string) CLIOption {
	return func(config *Config) error {
		validCriteria := []string{"gini", "entropy", "mse"}
		criterion = strings.ToLower(strings.TrimSpace(criterion))

		for _, valid := range validCriteria {
			if criterion == valid {
				config.Criterion = criterion
				return nil
			}
		}

		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("invalid criterion: %s (valid: %v)", criterion, validCriteria),
		}
	}
}

// WithVerbose enables or disables verbose output.
func WithVerbose(verbose bool) CLIOption {
	return func(config *Config) error {
		config.Verbose = verbose
		return nil
	}
}

// NewConfig creates a new Config with default values and applies the given options.
// Returns an error if any option validation fails.
func NewConfig(options ...CLIOption) (*Config, error) {
	config := &Config{
		// Default decision tree parameters
		MaxDepth:        10,
		MinSamplesSplit: 2,
		MinSamplesLeaf:  1,
		Criterion:       "entropy",
		Verbose:         false,
	}

	for _, option := range options {
		if err := option(config); err != nil {
			return nil, fmt.Errorf("invalid CLI option: %w", err)
		}
	}

	return config, nil
}

// Validate performs comprehensive validation of the configuration.
// It checks command validity, file existence, and parameter constraints.
// Returns the first validation error encountered for faster feedback.
func (c *Config) Validate() error {
	if err := c.validateCommand(); err != nil {
		return err
	}

	if err := c.validateInputFile(); err != nil {
		return err
	}

	if c.Command == "train" {
		if err := c.validateTrainingParams(); err != nil {
			return err
		}
	}

	if c.Command == "predict" {
		if err := c.validatePredictionParams(); err != nil {
			return err
		}
	}

	if err := c.validateDecisionTreeParams(); err != nil {
		return err
	}

	if err := c.validateLogConfig(); err != nil {
		return err
	}

	return nil
}

// validateCommand checks if the command is valid and specified.
func (c *Config) validateCommand() error {
	if c.Command == "" {
		return &CLIError{
			Op:  "validation",
			Err: errors.New("command not specified (use -c train or -c predict)"),
		}
	}

	validCommands := []string{"train", "predict"}
	for _, valid := range validCommands {
		if c.Command == valid {
			return nil
		}
	}

	return &CLIError{
		Op:      "validation",
		Command: c.Command,
		Err:     fmt.Errorf("invalid command: %s (valid: %v)", c.Command, validCommands),
	}
}

// validateInputFile performs comprehensive input file validation.
func (c *Config) validateInputFile() error {
	if c.InputFile == "" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     errors.New("input file not specified (use -i <input_data_file.csv>)"),
		}
	}

	// Clean and validate path
	cleanPath := filepath.Clean(c.InputFile)
	if filepath.IsAbs(c.InputFile) && cleanPath != c.InputFile {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     fmt.Errorf("invalid file path: %s", c.InputFile),
		}
	}

	// Validate file extension first (before checking existence)
	ext := strings.ToLower(filepath.Ext(cleanPath))
	if ext != ".csv" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     fmt.Errorf("invalid file extension: %s (expected .csv)", ext),
		}
	}

	// Check file existence and properties
	info, err := os.Stat(cleanPath)
	if os.IsNotExist(err) {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     fmt.Errorf("file does not exist: %s", cleanPath),
		}
	}
	if err != nil {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     fmt.Errorf("cannot access file: %w", err),
		}
	}

	if info.IsDir() {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "input",
			Err:     fmt.Errorf("path is a directory, not a file: %s", cleanPath),
		}
	}

	return nil
}

// validateTrainingParams validates parameters specific to training operations.
func (c *Config) validateTrainingParams() error {
	if c.TargetCol == "" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "target",
			Err:     errors.New("target column not specified for training (use -t <target_column>)"),
		}
	}

	if c.OutputFile == "" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "output",
			Err:     errors.New("output path not specified for training (use -o <output_tree.dt>)"),
		}
	}

	// Validate feature info file if provided
	if c.FeatureInfoFile != "" {
		if err := c.validateFeatureInfoFile(); err != nil {
			return err
		}
	}

	return nil
}

// validatePredictionParams validates parameters specific to prediction operations.
func (c *Config) validatePredictionParams() error {
	if c.ModelFile == "" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "model",
			Err:     errors.New("model file not specified for prediction (use -m <model_file.dt>)"),
		}
	}

	if _, err := os.Stat(c.ModelFile); os.IsNotExist(err) {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "model",
			Err:     fmt.Errorf("model file does not exist: %s", c.ModelFile),
		}
	}

	if c.OutputFile == "" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "output",
			Err:     errors.New("output path not specified for prediction (use -o <predictions.csv>)"),
		}
	}

	return nil
}

// validateFeatureInfoFile validates the feature metadata file.
func (c *Config) validateFeatureInfoFile() error {
	if _, err := os.Stat(c.FeatureInfoFile); os.IsNotExist(err) {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "metadata",
			Err:     fmt.Errorf("feature info file does not exist: %s", c.FeatureInfoFile),
		}
	}

	// Validate file extension
	ext := strings.ToLower(filepath.Ext(c.FeatureInfoFile))
	if ext != ".yaml" && ext != ".yml" {
		return &CLIError{
			Op:      "validation",
			Command: c.Command,
			Flag:    "metadata",
			Err:     fmt.Errorf("invalid metadata file extension: %s (expected .yaml or .yml)", ext),
		}
	}

	return nil
}

// validateDecisionTreeParams validates decision tree specific parameters.
func (c *Config) validateDecisionTreeParams() error {
	if c.MaxDepth <= 0 {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("max depth must be positive: %d", c.MaxDepth),
		}
	}

	if c.MinSamplesSplit < 2 {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("min samples split must be >= 2: %d", c.MinSamplesSplit),
		}
	}

	if c.MinSamplesLeaf < 1 {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("min samples leaf must be >= 1: %d", c.MinSamplesLeaf),
		}
	}

	validCriteria := []string{"gini", "entropy", "mse"}
	criterionValid := false
	for _, valid := range validCriteria {
		if c.Criterion == valid {
			criterionValid = true
			break
		}
	}

	if !criterionValid {
		return &CLIError{
			Op:  "validation",
			Err: fmt.Errorf("invalid criterion: %s (valid: %v)", c.Criterion, validCriteria),
		}
	}

	return nil
}

func (c *Config) validateLogConfig() error {
	if c.LogConfigPath != "" {
		ext := strings.ToLower(filepath.Ext(c.LogConfigPath))
		if ext != ".yaml" && ext != ".yml" {
			return &CLIError{
				Op:      "validation",
				Command: c.Command,
				Flag:    "log-config",
				Err:     fmt.Errorf("invalid log config file extension: %s (expected .yaml or .yml)", ext),
			}
		}
	}
	return nil
}

// ShowHelp displays comprehensive help information for the CLI.
func (c *Config) ShowHelp() {
	helpText := `Mulberri Decision Tree CLI

Usage:
  mulberri -c train -i data.csv -t target -o model.dt [options]
  mulberri -c predict -i data.csv -m model.dt -o predictions.csv [options]

Commands:
  train    Train a decision tree model from CSV data
  predict  Make predictions using a trained model

Required Flags:
  -c command        Command to execute (train|predict)
  -i input          Input CSV file path
  -t target         Target column name (training only)
  -o output         Output file path (model.dt for train, predictions.csv for predict)
  -m model          Model file path (prediction only)

Optional Flags:
  -f metadata       Feature metadata YAML file path
  --log-config      Logger configuration YAML file path
  --max-depth       Maximum tree depth (default: 10)
  --min-samples     Minimum samples to split a node (default: 2)
  --min-leaf        Minimum samples at leaf node (default: 1)
  --criterion       Splitting criterion: gini|entropy|mse (default: entropy)
  --verbose         Enable verbose output
  --help            Show this help message
  --version         Show version information

Examples:
  # Train a decision tree
  mulberri -c train -i training_data.csv -t species -o iris_model.dt

  # Train with metadata and custom parameters
  mulberri -c train -i data.csv -t target -o model.dt -f metadata.yaml --max-depth 5 --verbose

  # Make predictions
  mulberri -c predict -i test_data.csv -m iris_model.dt -o predictions.csv

  # Verbose prediction
  mulberri -c predict -i test_data.csv -m model.dt -o results.csv --verbose

For more information, visit: https://github.com/mulberri/mulberri`

	// Log the help display action and output the help text
	fmt.Println(helpText)
}

// ShowVersion displays version information.
func (c *Config) ShowVersion() {
	fmt.Println("Mulberri Decision Tree CLI v1.0.0")
	fmt.Println("Built with Go", "1.21+")
}
